<template>
  <div>
    <!-- 新增+筛选区 -->
    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 12px;">
      <van-button type="primary" size="small" @click="showAddDialog = true">新增用户</van-button>
      <van-field
        v-model="searchName"
        label="姓名筛选"
        placeholder="输入姓名"
        clearable
        style="flex: 1; margin-left: 8px;"
        @keyup.enter="fetchImages(1)"
      />
      <van-button type="primary" size="small" @click="fetchImages(1)">查询</van-button>
      <van-button type="default" size="small" @click="resetSearch">重置</van-button>
    </div>

    <!-- 新增用户 -->
    <van-dialog v-model:show="showAddDialog" title="新增用户" show-cancel-button @confirm="addUser">
      <van-field v-model="addUsername" label="用户名" required />
    </van-dialog>
    <!-- 新增成功后二维码弹窗 -->
    <van-dialog v-model:show="showQrDialog" title="用户二维码" show-cancel-button>
      <div style="text-align:center;">
        <div>用户ID: {{ newUserId }}</div>
        <qrcode-vue :value="String(newUserId)" :size="200" />
      </div>
    </van-dialog>

    <!-- 列表 -->
    <table border="1" cellspacing="0" cellpadding="4" style="width:100%;margin-bottom:16px;text-align:center;font-size:14px;">
      <thead>
        <tr>
          <th style="width:60px;">ID</th>
          <th style="width:120px;">姓名</th>
          <th style="width:120px;">操作</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="item in items" :key="item.id">
          <td>{{ item.id }}</td>
          <td>{{ item.username }}</td>
          <td>
            <div style="display: flex; gap: 4px; justify-content: center;">
              <van-button size="mini" @click="showImage(item)">结果</van-button>
              <van-button size="mini" @click="showQr(item)">二维码</van-button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>

    <!-- 分页 -->
    <van-pagination
      v-model="page"
      :total-items="total"
      :items-per-page="pageSize"
      @change="fetchImages"
      mode="simple"
      style="margin: 16px 0;"
    />

    <!-- 查看结果弹窗 -->
    <van-dialog v-model:show="showImageDialog" title="查看结果" show-cancel-button>
      <div v-if="currentImage" style="text-align:center;">
        <van-image :src="currentImage" width="100%" height="200" fit="contain" />
      </div>
    </van-dialog>

    <!-- 查看二维码弹窗 -->
    <van-dialog v-model:show="showQrDialog2" title="二维码" show-cancel-button>
      <div v-if="currentId" style="text-align:center;">
        <qrcode-vue :value="String(currentId)" :size="200" />
      </div>
    </van-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import axios from 'axios'
import QrcodeVue from 'qrcode.vue'

const items = ref([])
const total = ref(0)
const page = ref(1)
const pageSize = ref(10)
const searchName = ref('')

const showAddDialog = ref(false)
const addUsername = ref('')
const showQrDialog = ref(false)
const newUserId = ref(null)

const showImageDialog = ref(false)
const currentImage = ref('')
const showQrDialog2 = ref(false)
const currentId = ref(null)

const fetchImages = (p = page.value) => {
  page.value = p
  axios.get('http://**************:5000/api/images', {
    params: {
      page: page.value,
      page_size: pageSize.value,
      username: searchName.value
    }
  }).then(res => {
    items.value = res.data.items
    total.value = res.data.total
    // 调试输出
    console.log('接口返回数据:', res.data)
  })
}
fetchImages()

const addUser = () => {
  if (!addUsername.value) return
  axios.post('http://**************:5000/api/add_user', { username: addUsername.value })
    .then(res => {
      showAddDialog.value = false
      newUserId.value = res.data.id
      showQrDialog.value = true
      addUsername.value = ''
      fetchImages(1)
    })
}

const showImage = (row) => {
  currentImage.value = row.image1
  showImageDialog.value = true
}

const showQr = (row) => {
  currentId.value = row.id
  showQrDialog2.value = true
}

const resetSearch = () => {
  searchName.value = ''
  fetchImages(1)
}
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  margin-top: 60px;
}
</style>
